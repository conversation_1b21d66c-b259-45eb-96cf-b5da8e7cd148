<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentTransaction;
use App\Models\User;
use PayOS\PayOS;

class PayOSController extends Controller
{
    private $payOS;

    public function __construct()
    {
        $clientId = config('payos.client_id');
        $apiKey = config('payos.api_key');
        $checksumKey = config('payos.checksum_key');

        // Only initialize PayOS if all required credentials are present
        if ($clientId && $apiKey && $checksumKey) {
            $this->payOS = new PayOS($clientId, $apiKey, $checksumKey);
        }
    }

    /**
     * Create a payment link for token purchase
     */
    public function createPaymentLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_type' => 'required|string|in:basic,standard,premium'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid package type',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // Check if PayOS is properly configured
            if (!$this->payOS) {
                Log::error('PayOS not configured', [
                    'client_id' => config('payos.client_id') ? 'SET' : 'NOT SET',
                    'api_key' => config('payos.api_key') ? 'SET' : 'NOT SET',
                    'checksum_key' => config('payos.checksum_key') ? 'SET' : 'NOT SET',
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'PayOS is not properly configured. Please set PAYOS_CLIENT_ID, PAYOS_API_KEY, and PAYOS_CHECKSUM_KEY in your .env file.'
                ], 500);
            }

            $user = Auth::user();
            $packageType = $request->package_type;
            $packageConfig = config("payos.packages.{$packageType}");

            if (!$packageConfig) {
                return response()->json([
                    'success' => false,
                    'message' => 'Package not found'
                ], 404);
            }

            // Generate unique order code
            $orderCode = PaymentTransaction::generateOrderCode();

            // Create payment transaction record
            $transaction = PaymentTransaction::create([
                'user_id' => $user->id,
                'payos_order_code' => $orderCode,
                'package_type' => $packageType,
                'amount' => $packageConfig['amount'],
                'tokens' => $packageConfig['tokens'],
                'status' => PaymentTransaction::STATUS_PENDING,
                'description' => "Purchase {$packageConfig['tokens']} tokens"
            ]);

            // Prepare PayOS payment data (following PayOS documentation format)
            $paymentData = [
                "orderCode" => $orderCode,
                "amount" => $packageConfig['amount'],
                "description" => $transaction->description,
                "items" => [
                    [
                        'name' => $packageConfig['name'] . ' Token Package',
                        'price' => $packageConfig['amount'],
                        'quantity' => 1
                    ]
                ],
                "returnUrl" => config('payos.return_url'),
                "cancelUrl" => config('payos.cancel_url')
            ];

            Log::info('Creating PayOS payment link', [
                'user_id' => $user->id,
                'order_code' => $orderCode,
                'package_type' => $packageType,
                'payment_data' => $paymentData
            ]);

            // Create payment link with PayOS
            $response = $this->payOS->createPaymentLink($paymentData);

            Log::info('PayOS payment link created successfully', [
                'user_id' => $user->id,
                'order_code' => $orderCode,
                'package_type' => $packageType,
                'response' => $response
            ]);

            // Update transaction with PayOS response
            $transaction->update([
                'payos_payment_link_id' => $response['paymentLinkId'] ?? null,
                'payos_response' => $response
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'checkoutUrl' => $response['checkoutUrl'],
                    'orderCode' => $orderCode,
                    'amount' => $packageConfig['amount'],
                    'tokens' => $packageConfig['tokens'],
                    'package' => $packageConfig
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment link creation failed', [
                'user_id' => Auth::id(),
                'package_type' => $packageType ?? null,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'payos_configured' => $this->payOS !== null
            ]);

            // Return more specific error message for debugging
            $errorMessage = 'Failed to create payment link. Please try again.';
            if (app()->environment('local', 'testing')) {
                $errorMessage .= ' Error: ' . $e->getMessage();
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ], 500);
        }
    }

    /**
     * Get payment transaction status
     */
    public function getPaymentStatus(Request $request, $orderCode)
    {
        try {
            $user = Auth::user();
            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)
                ->where('user_id', $user->id)
                ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found'
                ], 404);
            }

            // Get latest status from PayOS
            $payosResponse = $this->payOS->getPaymentLinkInformation($orderCode);

            // Update local transaction status if needed
            if (isset($payosResponse['status'])) {
                $this->updateTransactionStatus($transaction, $payosResponse);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'orderCode' => $transaction->payos_order_code,
                    'status' => $transaction->status,
                    'amount' => $transaction->amount,
                    'tokens' => $transaction->tokens,
                    'package_type' => $transaction->package_type,
                    'created_at' => $transaction->created_at,
                    'paid_at' => $transaction->paid_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment status check failed', [
                'user_id' => Auth::id(),
                'order_code' => $orderCode,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment status'
            ], 500);
        }
    }

    /**
     * Handle PayOS webhook
     */
    public function handleWebhook(Request $request)
    {
        try {
            $webhookData = $request->all();

            Log::info('PayOS webhook received', [
                'data' => $webhookData,
                'headers' => $request->headers->all(),
                'raw_content' => $request->getContent()
            ]);

            // Check if this is a test webhook from PayOS (when setting up webhook URL)
            if (empty($webhookData) || (!isset($webhookData['data']) && !isset($webhookData['code']))) {
                Log::info('PayOS webhook test request detected');
                return response()->json(['message' => 'Webhook endpoint is working'], 200);
            }

            // Verify webhook signature using PayOS library
            try {
                $verifiedData = $this->payOS->verifyPaymentWebhookData($webhookData);
                Log::info('PayOS webhook signature verification successful', ['verified_data' => $verifiedData]);
            } catch (\Exception $e) {
                Log::warning('PayOS webhook signature verification failed', [
                    'error' => $e->getMessage(),
                    'webhook_data' => $webhookData
                ]);
                return response()->json(['message' => 'Invalid signature'], 400);
            }

            // Extract order code from verified data
            $orderCode = $verifiedData['orderCode'] ?? null;
            if (!$orderCode) {
                Log::warning('PayOS webhook missing order code', ['verified_data' => $verifiedData]);
                return response()->json(['message' => 'Missing order code'], 400);
            }

            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)->first();
            if (!$transaction) {
                Log::warning('PayOS webhook transaction not found', ['order_code' => $orderCode]);
                // For test webhooks with fake order codes, return success
                if ($orderCode == 123) {
                    Log::info('PayOS test webhook with fake order code, returning success');
                    return response()->json(['message' => 'Test webhook processed successfully']);
                }
                return response()->json(['message' => 'Transaction not found'], 404);
            }

            // Update transaction status based on verified webhook data
            $this->updateTransactionStatus($transaction, $verifiedData);

            return response()->json(['message' => 'Webhook processed successfully']);

        } catch (\Exception $e) {
            Log::error('PayOS webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            return response()->json(['message' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Cancel payment transaction
     */
    public function cancelPayment(Request $request, $orderCode)
    {
        try {
            $user = Auth::user();
            $transaction = PaymentTransaction::where('payos_order_code', $orderCode)
                ->where('user_id', $user->id)
                ->where('status', PaymentTransaction::STATUS_PENDING)
                ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found or cannot be cancelled'
                ], 404);
            }

            // Cancel payment link in PayOS
            $cancellationReason = $request->input('reason', 'Cancelled by user');

            $this->payOS->cancelPaymentLink($orderCode, $cancellationReason);

            // Update local transaction
            $transaction->markAsCancelled($cancellationReason);

            Log::info('PayOS payment cancelled', [
                'user_id' => $user->id,
                'order_code' => $orderCode,
                'reason' => $cancellationReason
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('PayOS payment cancellation failed', [
                'user_id' => Auth::id(),
                'order_code' => $orderCode,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel payment'
            ], 500);
        }
    }

    /**
     * Update transaction status based on PayOS response
     */
    private function updateTransactionStatus(PaymentTransaction $transaction, array $payosData)
    {
        // PayOS webhook sends status in different formats
        $status = $payosData['status'] ?? null;
        $code = $payosData['code'] ?? null;
        $desc = $payosData['desc'] ?? null;

        // Check if payment is successful based on PayOS webhook format
        $isPaymentSuccessful = ($status === 'PAID') ||
                              ($code === '00' && $desc === 'Thành công') ||
                              ($code === '00' && $desc === 'success');

        if ($isPaymentSuccessful && !$transaction->isPaid()) {
            $transaction->markAsPaid();

            // Add tokens to user account
            $user = $transaction->user;
            $user->tokens += $transaction->tokens;
            $user->save();

            Log::info('PayOS payment completed and tokens added', [
                'user_id' => $user->id,
                'order_code' => $transaction->payos_order_code,
                'tokens_added' => $transaction->tokens,
                'new_token_balance' => $user->tokens,
                'webhook_code' => $code,
                'webhook_desc' => $desc
            ]);
        } elseif ($status === 'CANCELLED' && !$transaction->isCancelled()) {
            $reason = $payosData['cancellationReason'] ?? 'Cancelled via PayOS';
            $transaction->markAsCancelled($reason);
        }

        // Update PayOS response data
        $transaction->update(['payos_response' => $payosData]);
    }


}
